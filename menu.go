package menukit

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/buger/jsonparser"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/pkg/log"
	"hexcloud.cn/hicloud/menukit/pkg/store"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
	"hexcloud.cn/hicloud/menukit/service/menu"
	"hexcloud.cn/hicloud/menukit/service/price"
)

// GetDataMode represents the data retrieval mode
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // Automatically fetch and merge data
	ModeFetchOnly                    // Only fetch to temporary storage
	ModeMergeTemp                    // Merge temporary data to production
	ModeProdOnly                     // Only use production data
)

// Options configures the menu data retrieval
type Options struct {
	// Basic configuration
	host        string // API host address
	token       string // Authentication token
	storeID     string // Store ID
	channelCode string // Channel code
	lang        string // Language
	partnerID   string

	// Directory configuration
	prodDir string // Production data directory
	tempDir string // Temporary data directory

	// Operation mode
	mode      GetDataMode // Data retrieval mode
	autoMerge bool        // Whether to auto-merge

	// Retry configuration
	maxRetries int           // Maximum retry attempts
	retryDelay time.Duration // Retry delay time

	// Other configuration
	expireInterval time.Duration // Data expiration interval

	// Store for data operations
	store store.Store // Store interface for data operations

	// Function configuration (deprecated, use store instead)
	getFunc  func(string) ([]byte, error) // Function to get data
	saveFunc func([]byte, string) error   // Function to save data

	//retun configuration
	returnBaseData   bool
	notFilterByPrice bool
}

// Option configures Options
type Option func(*Options)

// WithHost sets the API host address
func WithHost(host string) Option {
	return func(opts *Options) {
		opts.host = host
	}
}

// WithToken sets the authentication token
func WithToken(token string) Option {
	return func(opts *Options) {
		opts.token = token
	}
}

// WithStoreID sets the store ID
func WithStoreID(storeID string) Option {
	return func(opts *Options) {
		opts.storeID = storeID
	}
}

// WithChannelCode sets the channel code
func WithChannelCode(channelCode string) Option {
	return func(opts *Options) {
		opts.channelCode = channelCode
	}
}

// WithLang sets the language
func WithLang(lang string) Option {
	return func(opts *Options) {
		opts.lang = lang
	}
}

// WithProdDir sets the production data directory
func WithProdDir(dir string) Option {
	return func(opts *Options) {
		opts.prodDir = dir
	}
}

// WithTempDir sets the temporary data directory
func WithTempDir(dir string) Option {
	return func(opts *Options) {
		opts.tempDir = dir
	}
}

// WithMode sets the data retrieval mode
func WithMode(mode GetDataMode) Option {
	return func(opts *Options) {
		opts.mode = mode
	}
}

// WithAutoMerge sets whether to auto-merge
func WithAutoMerge(autoMerge bool) Option {
	return func(opts *Options) {
		opts.autoMerge = autoMerge
	}
}

// WithExpireInterval sets the data expiration interval
func WithExpireInterval(expireInterval time.Duration) Option {
	return func(opts *Options) {
		opts.expireInterval = expireInterval
	}
}

// WithMaxRetries sets the maximum retry attempts
func WithMaxRetries(maxRetries int) Option {
	return func(opts *Options) {
		opts.maxRetries = maxRetries
	}
}

// WithRetryDelay sets the retry delay time
func WithRetryDelay(retryDelay time.Duration) Option {
	return func(opts *Options) {
		opts.retryDelay = retryDelay
	}
}

func WithPartnerID(partnerID string) Option {
	return func(opts *Options) {
		opts.partnerID = partnerID
	}
}

func WithReturnBaseData(returnBaseData bool) Option {
	return func(o *Options) {
		o.returnBaseData = returnBaseData
	}
}
func WithNotFilterByPrice(notFilterByPrice bool) Option {
	return func(o *Options) {
		o.notFilterByPrice = notFilterByPrice
	}
}

// Getters for Options fields
func (o *Options) Host() string {
	return o.host
}

func (o *Options) Token() string {
	return o.token
}

func (o *Options) StoreID() string {
	return o.storeID
}

func (o *Options) ChannelCode() string {
	return o.channelCode
}

func (o *Options) Lang() string {
	return o.lang
}

func (o *Options) ProdDir() string {
	return o.prodDir
}

func (o *Options) TempDir() string {
	return o.tempDir
}

func (o *Options) Mode() GetDataMode {
	return o.mode
}

func (o *Options) AutoMerge() bool {
	return o.autoMerge
}

func (o *Options) MaxRetries() int {
	return o.maxRetries
}

func (o *Options) RetryDelay() time.Duration {
	return o.retryDelay
}

func (o *Options) ExpireInterval() time.Duration {
	return o.expireInterval
}
func (o *Options) Store() store.Store {
	return o.store
}

// WithStore sets the store for data operations
func WithStore(store store.Store) Option {
	return func(opts *Options) {
		opts.store = store
	}
}

// BaseDataRequest represents the price query request
type BaseDataRequest struct {
	BatchID string `json:"batchId"`
	Channel string `json:"channel"`
	StoreID string `json:"storeId"`
	// Query type 0:current price 2:modify 3:all
	QueryType               string `json:"queryType"`
	OriginProductListMd5    string `json:"originProductListMd5"`
	OriginProductSpuInfoMd5 string `json:"originProductSpuInfoMd5"`
	OriginAttrListMd5       string `json:"originAttrListMd5"`
	OriginPriceTypeMd5      string `json:"originPriceTypeMd5"`
}

// Page represents pagination information
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// Default retry configuration
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)

// sendBaseDataRequest sends a price query request and handles retry logic
// url: request URL
// req: request body
// token: authentication token
// maxRetries: maximum retry attempts
// retryDelay: retry interval time
func sendBaseDataRequest(ctx context.Context, params *GetMenuParams, opts *Options) ([]byte, error) {
	// 序列化请求体
	req := &BaseDataRequest{
		BatchID:                 params.BatchID,
		QueryType:               cast.ToString(int(params.QueryType)),
		OriginProductListMd5:    *params.LocalData.OriginProductListMd5,
		OriginProductSpuInfoMd5: *params.LocalData.OriginProductSpuInfoMd5,
		OriginAttrListMd5:       *params.LocalData.OriginAttrListMd5,
		OriginPriceTypeMd5:      *params.LocalData.OriginPriceTypeMd5,
		Channel:                 opts.channelCode,
		StoreID:                 opts.storeID,
	}
	url := fmt.Sprintf("%s%s", opts.host, model.DefaultBaseDataAPi)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 重试逻辑
	var respBody []byte
	var lastErr error

	for i := 0; i <= opts.maxRetries; i++ {
		// 如果不是第一次尝试，等待一段时间再重试
		if i > 0 {
			time.Sleep(opts.retryDelay)
		}
		// 创建HTTP请求
		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
		}

		// 设置请求头
		httpReq.Header.Set("Content-Type", "application/json")

		httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", opts.token))

		// 设置语言

		httpReq.Header.Set("Accept-Language", opts.lang)

		// 设置租户

		httpReq.Header.Set("partner_id", opts.partnerID)

		respBody, err = getHttpResponse(httpReq, model.GlobalHTTPClient)
		if err != nil {
			lastErr = err
			continue
		}
		return respBody, nil
	}

	// 所有重试都失败了，返回最后一个错误
	return nil, lastErr
}

// PullBaseData pulls menu base information from API
// host: API host address
// token: authentication token
// storeID: store ID
// saveFunc: function to save price information
// getFunc: function to get existing price information
func PullBaseData(ctx context.Context, params *GetMenuParams, opts *Options) (*menu.ProductListOpt, error) {
	// Get batch ID and query type
	var batchIDStr string
	var queryType model.PriceQueryType
	currentPriceInfo := params.LocalData.PriceInfo
	batchIDStr, queryType, err := price.GetBatchIdAndQueryType(currentPriceInfo, opts.ExpireInterval())
	if err != nil {
		log.Logger.Errorf("Failed to get batch ID and query type: %v, changed to full fetch (may be first fetch)", err)
	}

	// Convert string batch ID to uint64 for params
	params.BatchID = batchIDStr
	params.QueryType = queryType

	start := time.Now()
	respBody, err := sendBaseDataRequest(ctx, params, opts)
	if err != nil {
		return nil, err
	}
	log.Logger.Infof("Fetch base cloud data: baseData, size: %s, time: %v", utils.FormatDataSize(len(respBody)), time.Since(start))

	priceInfo := &model.PriceInfo{}
	priceVersionInfo := &model.StorePriceVersionInfoResponse{}
	g, _ := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		var hasUpdated bool
		priceInfo, hasUpdated, err = price.BuildPriceInfoByResponse(respBody, currentPriceInfo, params.QueryType)
		if err != nil {
			return err
		}
		if hasUpdated {
			priceInfoBs, err := json.Marshal(priceInfo)
			if err != nil {
				return err
			}
			if err := opts.store.Save(priceInfoBs, model.DefaultPriceFileName); err != nil {
				return fmt.Errorf("failed to save price information: %w", err)
			}
		}
		return err
	})
	g.Wait()
	params.LocalData.PriceVersionInfo = priceVersionInfo
	params.LocalData, err = buildAndSaveBaseData(ctx, params.LocalData, respBody, opts.store.Save)
	if err != nil {
		return nil, err
	}
	params.LocalData.PriceInfo = priceInfo
	params.LocalData.PriceVersionInfo = priceVersionInfo
	return params.LocalData, nil
}

// GetMenuDataLegacy gets menu data from file or cloud (legacy function for backward compatibility)
func GetMenuDataLegacy(ctx context.Context, options ...Option) (*model.ProductListResponse, error) {
	// Apply option configuration

	var menuOpts *menu.ProductListOpt
	var err error
	opts := applyPullPriceOptions(options...)
	log.Logger.Infof("Start getting menu data - Store ID: %s, Channel: %s", opts.storeID, opts.channelCode)

	// Try to fetch data from cloud first
	start := time.Now()
	//{
	//    "batchId": "5020927675187920896",
	//    "channel": "POS",params
	//    "storeId": "4972806615293067264",
	//    "queryType": "2",
	//    "originProductListMd5":"89de39d677222fbb2f278053146023d4",
	//    "originProductSpuInfoMd5":"185e2bf66567f6bc9efc651436da0406",
	//    "originAttrListMd5":"4aa46649699733c0edf6386cc5e6523b",
	//    "originPriceTypeMd5":"a291c2d9a884075147337328a12fddb6"
	//}
	params := &GetMenuParams{}
	params.LocalData, err = loadLocalMenuData(ctx, opts)
	log.Logger.Infof("loadLocalMenuData time: %v", time.Since(start))
	if err != nil {
		log.Logger.Errorf("Local cache read failed: local error=%v", err)
	}
	menuOpts, err = PullBaseData(ctx, params, opts)
	elapsed := time.Since(start)
	log.Logger.Infof("Cloud data fetch time: %s", elapsed.String())
	if err != nil {
		log.Logger.Warnf("Failed to fetch cloud data: %v, trying to use local cache data", err)
		// Use err group to read local data concurrently
		menuOpts = params.LocalData
		log.Logger.Info("Successfully used local cache data")
	} else {
		log.Logger.Info("Successfully fetched data from cloud")
	}

	// Generate channel product list
	start = time.Now()
	menuOpts.NotFilterByPrice = opts.notFilterByPrice

	result, err := menu.GetChannelProductList(ctx, opts.channelCode, menuOpts)
	log.Logger.Infof("Assembly time: %v", time.Since(start))
	if err != nil {
		return nil, fmt.Errorf("failed to generate channel product list: %w", err)
	}

	return result, nil
}

// applyPullPriceOptions applies pull price option configuration
func applyPullPriceOptions(options ...Option) *Options {
	opts := &Options{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		store:          store.NewFileStore("./"),
	}
	for _, option := range options {
		option(opts)
	}
	return opts
}

// Define file read tasks
type fileTask struct {
	fileName        string
	unmarshalTarget any
	name            string
	md5String       *string
}

// loadLocalMenuData loads local menu data concurrently
func loadLocalMenuData(ctx context.Context, opts *Options) (*menu.ProductListOpt, error) {
	menuOpts := &menu.ProductListOpt{
		ProductList:             make([]*model.Category, 0),
		ProductInfo:             make([]*model.Product, 0),
		ProductAttr:             make([]*model.AdditionalAttribute, 0),
		PriceInfo:               &model.PriceInfo{},
		PriceTypeInfo:           make([]*model.PriceType, 0),
		StockList:               make([]map[string]interface{}, 0),
		OriginPriceTypeMd5:      new(string),
		OriginProductSpuInfoMd5: new(string),
		OriginAttrListMd5:       new(string),
		OriginProductListMd5:    new(string),
		OriginStockListMd5:      new(string),
	}
	g, ctx := errgroup.WithContext(ctx)
	tasks := []fileTask{
		{model.DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "price_type", menuOpts.OriginPriceTypeMd5},
		{model.DefaultProductInfoFileName, &menuOpts.ProductInfo, "product_info", menuOpts.OriginProductSpuInfoMd5},
		{model.DefaultProductAttrFileName, &menuOpts.ProductAttr, "product_attr", menuOpts.OriginAttrListMd5},
		{model.DefaultProductListFileName, &menuOpts.ProductList, "product_list", menuOpts.OriginProductListMd5},
		{model.DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}

	// 单独处理价格信息
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled before loading price info: %w", ctx.Err())
		default:
			getter := opts.store.Get
			saver := opts.store.Save
			data, err := price.GetPriceInfoFromFile(0, getter, saver)
			if err != nil {
				return fmt.Errorf("failed to read %s file: %w", "price", err)
			}
			menuOpts.PriceInfo = data
			return nil
		}
	})

	// 并发读取所有本地文件
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return fmt.Errorf("context cancelled before loading %s: %w", task.name, ctx.Err())
			default:
				start := time.Now()
				data, err := opts.store.Get(task.fileName)
				if err != nil {
					return fmt.Errorf("failed to read %s file: %w", task.name, err)
				}
				md5, _ := jsonparser.GetString(data, "md5")
				*task.md5String = md5
				data, _, _, _ = jsonparser.Get(data, "rows")
				if len(data) == 0 {
					log.Logger.Infof("local file %s is empty, skipping", task.name)
					return nil
				}
				err = json.Unmarshal(data, task.unmarshalTarget)
				if err != nil {
					return fmt.Errorf("failed to parse %s file: %w", task.name, err)
				}
				log.Logger.Infof("successfully read local file: %s, size: %s, time: %v", task.name, utils.FormatDataSize(len(data)), time.Since(start))
				return nil
			}
		})
	}

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("failed to read local files concurrently: %w", err)
	}

	log.Logger.Info("all local files read successfully")
	return menuOpts, nil
}

// Get and other information (product_info product_attr price_type product_list) return map[string][]byte filename and content
func buildAndSaveBaseData(ctx context.Context, menuOpts *menu.ProductListOpt, resp []byte, savefunc func([]byte, string) error) (*menu.ProductListOpt, error) {
	if menuOpts == nil {
		menuOpts = &menu.ProductListOpt{
			ProductList:   make([]*model.Category, 0),
			ProductInfo:   make([]*model.Product, 0),
			ProductAttr:   make([]*model.AdditionalAttribute, 0),
			PriceInfo:     &model.PriceInfo{},
			PriceTypeInfo: make([]*model.PriceType, 0),
		}
	}
	tasks := []fileTask{
		{model.DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "priceType", menuOpts.OriginPriceTypeMd5},
		{model.DefaultProductInfoFileName, &menuOpts.ProductInfo, "productSpuInfo", menuOpts.OriginProductSpuInfoMd5},
		{model.DefaultProductAttrFileName, &menuOpts.ProductAttr, "attrList", menuOpts.OriginAttrListMd5},
		{model.DefaultProductListFileName, &menuOpts.ProductList, "productList", menuOpts.OriginProductListMd5},
		{model.DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}
	g, ctx := errgroup.WithContext(ctx)
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return fmt.Errorf("context cancelled before processing %s: %w", task.name, ctx.Err())
			default:
				data, _, _, err := jsonparser.Get(resp, "payload", task.name)
				if err != nil {
					return fmt.Errorf("failed to get %s: %w", task.name, err)
				}
				rows, _, _, err := jsonparser.Get(data, "rows")
				if err != nil {
					return fmt.Errorf("failed to get %s.rows: %w", task.name, err)
				}
				if cast.ToString(rows) == "null" {
					log.Logger.Infof("%s: local data is consistent with cloud data, using local data", task.name)
					return nil
				}
				err = json.Unmarshal(rows, task.unmarshalTarget)
				log.Logger.Infof("successfully read cloud data: %s, size: %s", task.name, utils.FormatDataSize(len(data)))
				if err := savefunc(data, task.fileName); err != nil {
					return fmt.Errorf("failed to save %s: %w", task.name, err)
				}
				return nil
			}
		})
	}
	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("failed to read cloud data concurrently: %w", err)
	}

	return menuOpts, nil
}

func getHttpResponse(httpReq *http.Request, client *http.Client) ([]byte, error) {
	// Send request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close() // Ensure response body is closed before function exits

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned error status code: %d", resp.StatusCode)
	}

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status_code
	statusCode, err := jsonparser.GetInt(respBody, "status_code")
	if err != nil {
		return nil, fmt.Errorf("failed to read response status code: %w", err)
	}
	if statusCode != 200 {
		return nil, fmt.Errorf("API returned error status code: %d, resp:%s", statusCode, respBody)
	}
	// Request successful, break loop
	return respBody, nil

}
