package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"hexcloud.cn/hicloud/menukit"
)

var (
	// Global flags
	host        string
	token       string
	storeID     uint64
	channelCode string
	lang        string
	prodDir     string
	tempDir     string
	maxRetries  int
	retryDelay  time.Duration
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "menucli",
	Short: "MenuKit 命令行工具",
	Long: `MenuKit 命令行工具用于获取和处理多渠道商品菜单数据。
支持多种操作模式，包括自动合并、仅拉取、合并暂存和仅使用生产数据。`,
}

// autoMergeCmd represents the auto-merge command
var autoMergeCmd = &cobra.Command{
	Use:   "auto-merge",
	Short: "自动拉取并合并数据",
	Long:  `从云端拉取最新数据并自动合并到生产目录`,
	Run: func(cmd *cobra.Command, args []string) {
		start := time.Now()
		menuData, err := menukit.GetMenuData(
			context.Background(),
			menukit.WithHost(host),
			menukit.WithToken(token),
			menukit.WithStoreID(cast.ToString(storeID)),
			menukit.WithChannelCode(channelCode),
			menukit.WithLang(lang),
			menukit.WithProdDir(prodDir),
			menukit.WithTempDir(tempDir),
			menukit.WithMaxRetries(maxRetries),
			menukit.WithRetryDelay(retryDelay),
			menukit.WithMode(menukit.ModeAutoMerge),
		)
		elapsed := time.Since(start)

		if err != nil {
			log.Fatalf("获取菜单数据失败: %v", err)
		}

		// Calculate data size
		dataSize := 0
		if menuData != nil && menuData.Payload != nil {
			// This is just an example, actual data size calculation may require more complex methods
			dataSize = len(fmt.Sprintf("%+v", menuData))
		}

		fmt.Printf("操作完成:\n")
		fmt.Printf("  模式: 自动拉取并合并\n")
		fmt.Printf("  数据大小: %s\n", formatDataSize(dataSize))
		fmt.Printf("  处理时间: %v\n", elapsed)
	},
}

// fetchOnlyCmd represents the fetch-only command
var fetchOnlyCmd = &cobra.Command{
	Use:   "fetch-only",
	Short: "仅拉取数据到暂存目录",
	Long:  `从云端拉取最新数据仅保存到暂存目录，不合并到生产目录`,
	Run: func(cmd *cobra.Command, args []string) {
		start := time.Now()
		err := menukit.FetchToTemp(
			context.Background(),
			menukit.WithHost(host),
			menukit.WithToken(token),
			menukit.WithStoreID(cast.ToString(storeID)),
			menukit.WithTempDir(tempDir),
			menukit.WithMaxRetries(maxRetries),
			menukit.WithRetryDelay(retryDelay),
		)
		elapsed := time.Since(start)

		if err != nil {
			log.Fatalf("拉取数据到暂存目录失败: %v", err)
		}

		fmt.Printf("操作完成:\n")
		fmt.Printf("  模式: 仅拉取到暂存目录\n")
		fmt.Printf("  处理时间: %v\n", elapsed)
	},
}

// mergeTempCmd represents the merge-temp command
var mergeTempCmd = &cobra.Command{
	Use:   "merge-temp",
	Short: "合并暂存数据到生产目录",
	Long:  `将暂存目录的数据合并到生产目录`,
	Run: func(cmd *cobra.Command, args []string) {
		start := time.Now()
		err := menukit.MergeTempToProd(
			context.Background(),
			menukit.WithProdDir(prodDir),
			menukit.WithTempDir(tempDir),
		)
		elapsed := time.Since(start)

		if err != nil {
			log.Fatalf("合并暂存数据到生产目录失败: %v", err)
		}

		fmt.Printf("操作完成:\n")
		fmt.Printf("  模式: 合并暂存到生产\n")
		fmt.Printf("  处理时间: %v\n", elapsed)
	},
}

// prodOnlyCmd represents the prod-only command
var prodOnlyCmd = &cobra.Command{
	Use:   "prod-only",
	Short: "仅使用生产目录数据",
	Long:  `仅从生产目录获取数据，不进行任何网络请求`,
	Run: func(cmd *cobra.Command, args []string) {
		start := time.Now()
		menuData, err := menukit.GetMenuData(
			context.Background(),
			menukit.WithStoreID(cast.ToString(storeID)),
			menukit.WithChannelCode(channelCode),
			menukit.WithLang(lang),
			menukit.WithProdDir(prodDir),
			menukit.WithMode(menukit.ModeProdOnly),
		)
		elapsed := time.Since(start)

		if err != nil {
			log.Fatalf("从生产目录获取数据失败: %v", err)
		}

		// Calculate data size
		dataSize := 0
		if menuData != nil && menuData.Payload != nil {
			// This is just an example, actual data size calculation may require more complex methods
			dataSize = len(fmt.Sprintf("%+v", menuData))
		}

		fmt.Printf("操作完成:\n")
		fmt.Printf("  模式: 仅使用生产数据\n")
		fmt.Printf("  数据大小: %s\n", formatDataSize(dataSize))
		fmt.Printf("  处理时间: %v\n", elapsed)
	},
}

// hasUpdateCmd represents the has-update command
var hasUpdateCmd = &cobra.Command{
	Use:   "has-update",
	Short: "检查暂存目录是否有更新",
	Long:  `检查暂存目录的数据是否比生产目录新`,
	Run: func(cmd *cobra.Command, args []string) {
		start := time.Now()
		hasUpdate, err := menukit.HasTempUpdate(
			menukit.WithProdDir(prodDir),
			menukit.WithTempDir(tempDir),
		)
		elapsed := time.Since(start)

		if err != nil {
			log.Fatalf("检查更新失败: %v", err)
		}

		fmt.Printf("检查完成:\n")
		fmt.Printf("  模式: 检查暂存更新\n")
		fmt.Printf("  有更新: %t\n", hasUpdate)
		fmt.Printf("  处理时间: %v\n", elapsed)
	},
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	// Set global flags
	rootCmd.PersistentFlags().StringVar(&host, "host", "https://hipos-saas-qa.hexcloud.cn", "API主机地址")
	rootCmd.PersistentFlags().StringVar(&token, "token", "", "认证令牌")
	rootCmd.PersistentFlags().Uint64Var(&storeID, "store-id", 0, "店铺ID")
	rootCmd.PersistentFlags().StringVar(&channelCode, "channel", "POS", "渠道代码")
	rootCmd.PersistentFlags().StringVar(&lang, "lang", "zh-CN", "语言")
	rootCmd.PersistentFlags().StringVar(&prodDir, "prod-dir", "./prod", "生产数据目录")
	rootCmd.PersistentFlags().StringVar(&tempDir, "temp-dir", "./temp", "暂存数据目录")
	rootCmd.PersistentFlags().IntVar(&maxRetries, "max-retries", 3, "最大重试次数")
	rootCmd.PersistentFlags().DurationVar(&retryDelay, "retry-delay", 500*time.Millisecond, "重试延迟时间")

	// Add subcommands to root command
	rootCmd.AddCommand(autoMergeCmd)
	rootCmd.AddCommand(fetchOnlyCmd)
	rootCmd.AddCommand(mergeTempCmd)
	rootCmd.AddCommand(prodOnlyCmd)
	rootCmd.AddCommand(hasUpdateCmd)
}

// formatDataSize converts bytes to a more readable format
func formatDataSize(bytes int) string {
	if bytes < 1024 {
		return fmt.Sprintf("%d B", bytes)
	} else if bytes < 1024*1024 {
		return fmt.Sprintf("%.2f KB", float64(bytes)/1024)
	} else if bytes < 1024*1024*1024 {
		return fmt.Sprintf("%.2f MB", float64(bytes)/(1024*1024))
	}
	return fmt.Sprintf("%.2f GB", float64(bytes)/(1024*1024*1024))
}
