package main

import (
	"context"
	"fmt"
	"hexcloud.cn/hicloud/menukit"
	"log"
)

func main() {
	// Example usage of MenuKit
	ctx := context.Background()

	// 1. Auto fetch and merge (default mode)
	menuData, err := menukit.GetMenuData(
		ctx,
		menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
		menukit.WithToken("your-auth-token"),
		menukit.WithStoreID("12345"),
		menukit.WithChannelCode("POS"),
		menukit.WithProdDir("./prod"),
		menukit.WithTempDir("./temp"),
	)
	if err != nil {
		log.Fatalf("Failed to get menu data: %v", err)
	}

	fmt.Printf("Successfully fetched menu data with %d categories\n", len(menuData.Payload.Category))

	// 2. Fetch to temporary directory only
	err = menukit.FetchToTemp(
		ctx,
		menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
		menukit.WithToken("your-auth-token"),
		menukit.WithStoreID("12345"),
		menukit.WithTempDir("./temp"),
	)
	if err != nil {
		log.Fatalf("Failed to fetch to temp: %v", err)
	}

	fmt.Println("Successfully fetched data to temp directory")

	// 3. Check if there are temporary updates
	hasUpdate, err := menukit.HasTempUpdate(
		menukit.WithProdDir("./prod"),
		menukit.WithTempDir("./temp"),
	)
	if err != nil {
		log.Fatalf("Failed to check for updates: %v", err)
	}

	if hasUpdate {
		fmt.Println("There are updates in the temp directory")

		// 4. Merge temporary data to production data
		err = menukit.MergeTempToProd(
			ctx,
			menukit.WithProdDir("./prod"),
			menukit.WithTempDir("./temp"),
		)
		if err != nil {
			log.Fatalf("Failed to merge temp to prod: %v", err)
		}

		fmt.Println("Successfully merged temp to prod")

		// 5. Re-fetch merged data
		menuData, err = menukit.GetMenuData(
			ctx,
			menukit.WithProdDir("./prod"),
			menukit.WithMode(menukit.ModeProdOnly),
		)
		if err != nil {
			log.Fatalf("Failed to get menu data from prod: %v", err)
		}

		fmt.Printf("Successfully fetched merged menu data with %d categories\n", len(menuData.Payload.Category))
	} else {
		fmt.Println("No updates in the temp directory")
	}
}
