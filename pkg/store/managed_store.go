package store

import (
	"fmt"
)

// ManagedStore wraps StorageManager to implement the Store interface
// This allows the new storage architecture to be used with existing code
type ManagedStore struct {
	manager *StorageManager
	env     Environment
}

// NewManagedStore creates a new managed store that operates on a specific environment
func NewManagedStore(manager *StorageManager, env Environment) *ManagedStore {
	return &ManagedStore{
		manager: manager,
		env:     env,
	}
}

// Get retrieves data by key from the managed environment
func (ms *ManagedStore) Get(key string) ([]byte, error) {
	return ms.manager.GetFromEnv(ms.env, key)
}

// Save stores data with key to the managed environment
func (ms *ManagedStore) Save(data []byte, key string) error {
	return ms.manager.SaveToEnv(ms.env, data, key)
}

// Config returns the store configuration
func (ms *ManagedStore) Config() *StoreConfig {
	config := ms.manager.Config()
	config.Options["managed_environment"] = string(ms.env)
	return config
}

// GetManager returns the underlying storage manager
func (ms *ManagedStore) GetManager() *StorageManager {
	return ms.manager
}

// CrossStore interface implementation for ManagedStore
func (ms *ManagedStore) CrossRead(key string) ([]byte, error) {
	return ms.manager.CrossRead(key)
}

func (ms *ManagedStore) CrossWrite(data []byte, key string) error {
	return ms.manager.CrossWrite(data, key)
}

func (ms *ManagedStore) CrossReadWrite(key string, processor func([]byte) ([]byte, error)) error {
	return ms.manager.CrossReadWrite(key, processor)
}

func (ms *ManagedStore) MergeToProduction(key string) error {
	return ms.manager.MergeEnvironments(EnvStaging, EnvProduction, []string{key})
}

func (ms *ManagedStore) HasStagingUpdate(key string) (bool, error) {
	return ms.manager.HasUpdates(EnvStaging, EnvProduction, []string{key})
}

// StorageManagerBuilder helps build a complete storage manager with multiple environments
type StorageManagerBuilder struct {
	manager *StorageManager
}

// NewStorageManagerBuilder creates a new builder
func NewStorageManagerBuilder() *StorageManagerBuilder {
	return &StorageManagerBuilder{
		manager: NewStorageManager(),
	}
}

// WithFileBackend adds a file backend for the specified environment
func (smb *StorageManagerBuilder) WithFileBackend(env Environment, baseDir string) *StorageManagerBuilder {
	backend := NewFileBackend(baseDir)
	smb.manager.RegisterBackend(env, backend)
	return smb
}

// WithDefaultEnvironment sets the default environment
func (smb *StorageManagerBuilder) WithDefaultEnvironment(env Environment) *StorageManagerBuilder {
	smb.manager.SetDefaultEnvironment(env)
	return smb
}

// Build returns the configured storage manager
func (smb *StorageManagerBuilder) Build() *StorageManager {
	return smb.manager
}

// BuildManagedStore returns a managed store for the specified environment
func (smb *StorageManagerBuilder) BuildManagedStore(env Environment) *ManagedStore {
	return NewManagedStore(smb.manager, env)
}

// Convenience functions for common configurations

// NewDualFileStore creates a managed store with production and staging file backends
func NewDualFileStore(prodDir, stagingDir string) *ManagedStore {
	manager := NewStorageManagerBuilder().
		WithFileBackend(EnvProduction, prodDir).
		WithFileBackend(EnvStaging, stagingDir).
		WithDefaultEnvironment(EnvProduction).
		Build()
	
	return NewManagedStore(manager, EnvProduction)
}

// NewTripleFileStore creates a managed store with production, staging, and temp file backends
func NewTripleFileStore(prodDir, stagingDir, tempDir string) *ManagedStore {
	manager := NewStorageManagerBuilder().
		WithFileBackend(EnvProduction, prodDir).
		WithFileBackend(EnvStaging, stagingDir).
		WithFileBackend(EnvTemp, tempDir).
		WithDefaultEnvironment(EnvProduction).
		Build()
	
	return NewManagedStore(manager, EnvProduction)
}

// BusinessOperations provides high-level business operations
type BusinessOperations struct {
	store *ManagedStore
}

// NewBusinessOperations creates business operations wrapper
func NewBusinessOperations(store *ManagedStore) *BusinessOperations {
	return &BusinessOperations{store: store}
}

// LoadFromProdAndSaveToStaging implements the cross-read-write pattern for business logic
// This is the key function for your use case: read from production, process, save to staging
func (bo *BusinessOperations) LoadFromProdAndSaveToStaging(key string, processor func([]byte) ([]byte, error)) error {
	// Check if the store supports cross operations
	if crossStore, ok := bo.store.(CrossStore); ok {
		return crossStore.CrossReadWrite(key, processor)
	}
	
	// Fallback: manual cross operation
	manager := bo.store.GetManager()
	return manager.CrossReadWrite(key, processor)
}

// MergeAllStagingToProduction merges all staging data to production
func (bo *BusinessOperations) MergeAllStagingToProduction(keys []string) error {
	manager := bo.store.GetManager()
	return manager.MergeEnvironments(EnvStaging, EnvProduction, keys)
}

// HasAnyUpdates checks if there are any updates in staging
func (bo *BusinessOperations) HasAnyUpdates(keys []string) (bool, error) {
	manager := bo.store.GetManager()
	return manager.HasUpdates(EnvStaging, EnvProduction, keys)
}

// GetFromEnvironment gets data from a specific environment
func (bo *BusinessOperations) GetFromEnvironment(env Environment, key string) ([]byte, error) {
	manager := bo.store.GetManager()
	return manager.GetFromEnv(env, key)
}

// SaveToEnvironment saves data to a specific environment
func (bo *BusinessOperations) SaveToEnvironment(env Environment, data []byte, key string) error {
	manager := bo.store.GetManager()
	return manager.SaveToEnv(env, data, key)
}
