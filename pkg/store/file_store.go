package store

import (
	"bufio"
	"bytes"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"hexcloud.cn/hicloud/menukit/pkg/utils"
)

// 文件读取缓冲区大小
const bufferSize = 64 * 1024 // 64KB

// 内存缓存，用于存储最近读取的文件内容
var (
	fileCache      = make(map[string][]byte)
	fileCacheMutex sync.RWMutex
	cacheTTL       = 5 * time.Minute // 缓存5分钟
)

// FileStore implements the Store interface for file-based storage
type FileStore struct {
	dir string
}

// NewFileStore creates a new file-based store
func NewFileStore(dir string) *FileStore {
	return &FileStore{
		dir: dir,
	}
}

// Get retrieves data from a file
func (fs *FileStore) Get(fileName string) ([]byte, error) {
	f := DefaultGetFuncWithBuffer(fs.dir)
	return f(fileName)
}

// Save stores data to a file
func (fs *FileStore) Save(data []byte, fileName string) error {
	f := DefaultSaveFuncWithBuffer(fs.dir)
	return f(data, fileName)
}

// Config returns the store configuration
func (fs *FileStore) Config() *StoreConfig {
	return &StoreConfig{
		Type:     "file",
		Path:     fs.dir,
		ReadOnly: false,
		Options:  make(map[string]interface{}),
	}
}

// 带缓冲的文件读取函数
func bufferedReadFile(fileName string) ([]byte, error) {
	// 首先检查缓存
	fileCacheMutex.RLock()
	if cached, exists := fileCache[fileName]; exists {
		fileCacheMutex.RUnlock()
		return cached, nil
	}
	fileCacheMutex.RUnlock()

	// 缓存未命中，从文件系统读取
	file, err := os.Open(fileName)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 使用缓冲读取器
	reader := bufio.NewReaderSize(file, bufferSize)
	var buffer bytes.Buffer

	// 读取文件内容
	_, err = io.CopyBuffer(&buffer, reader, make([]byte, bufferSize))
	if err != nil {
		return nil, err
	}

	// 更新缓存
	fileCacheMutex.Lock()
	fileCache[fileName] = buffer.Bytes()
	// 设置定时清理缓存
	time.AfterFunc(cacheTTL, func() {
		fileCacheMutex.Lock()
		delete(fileCache, fileName)
		fileCacheMutex.Unlock()
	})
	fileCacheMutex.Unlock()

	return buffer.Bytes(), nil
}

// 提供手动清理缓存功能
func ClearFileCache(fileName string) {
	fileCacheMutex.Lock()
	delete(fileCache, fileName)
	fileCacheMutex.Unlock()
}

// 带缓冲的文件写入函数
func bufferedWriteFile(fileName string, data []byte) error {
	// 确保目录存在
	dir := filepath.Dir(fileName)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败: %w", err)
		}
	}

	// 创建文件
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用缓冲写入器
	writer := bufio.NewWriterSize(file, bufferSize)
	_, err = writer.Write(data)
	if err != nil {
		return err
	}

	// 确保数据写入磁盘
	return writer.Flush()
}

// 改进的DefaultGetFunc，使用带缓冲的文件读取
func DefaultGetFuncWithBuffer(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := utils.GetFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		if savePath == "" {
			return nil, errors.New("保存路径不能为空")
		}
		// 使用带缓冲的文件读取
		return bufferedReadFile(savePath)
	}
}

// 改进的DefaultSaveFunc，使用带缓冲的文件写入
func DefaultSaveFuncWithBuffer(dir string) func([]byte, string) error {
	return func(data []byte, fileName string) error {
		savePath := utils.GetFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("保存路径不能为空")
		}
		// 使用带缓冲的文件写入
		return bufferedWriteFile(savePath, data)
	}
}

func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(priceInfo []byte, fileName string) error {
		savePath := utils.GetFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("保存路径不能为空")
		}
		dir := filepath.Dir(savePath)
		if dir != "" && dir != "." { // 只为子目录创建文件夹
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录失败: %w", err)
			}
		}
		// 总是尝试写入文件
		if err := os.WriteFile(savePath, priceInfo, 0644); err != nil {
			return fmt.Errorf("写入文件失败: %w", err)
		}
		return nil
	}
}

func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := utils.GetFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		// get 从文件中读取价格信息
		if savePath == "" {
			return nil, errors.New("保存路径不能为空")
		}
		// 读取文件
		return os.ReadFile(savePath)
	}
}
