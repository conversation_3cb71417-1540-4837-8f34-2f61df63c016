package store

import (
	"fmt"
	"sync"
)

// Environment represents different storage environments
type Environment string

const (
	EnvProduction Environment = "production"
	EnvStaging    Environment = "staging"
	EnvTemp       Environment = "temp"
)

// StorageBackend defines the interface for different storage implementations
type StorageBackend interface {
	// Get retrieves data by key from the backend
	Get(key string) ([]byte, error)
	
	// Save stores data with key to the backend
	Save(data []byte, key string) error
	
	// Delete removes data by key from the backend
	Delete(key string) error
	
	// Exists checks if a key exists in the backend
	Exists(key string) (bool, error)
	
	// Config returns the backend configuration
	Config() *BackendConfig
}

// BackendConfig represents the configuration for a storage backend
type BackendConfig struct {
	Type    string                 // Backend type (file, database, memory, etc.)
	Path    string                 // Base path or connection string
	Options map[string]interface{} // Backend-specific options
}

// StorageManager manages multiple storage environments and backends
type StorageManager struct {
	backends map[Environment]StorageBackend
	mutex    sync.RWMutex
	
	// Default environment for operations
	defaultEnv Environment
}

// NewStorageManager creates a new storage manager
func NewStorageManager() *StorageManager {
	return &StorageManager{
		backends:   make(map[Environment]StorageBackend),
		defaultEnv: EnvProduction,
	}
}

// RegisterBackend registers a storage backend for a specific environment
func (sm *StorageManager) RegisterBackend(env Environment, backend StorageBackend) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.backends[env] = backend
}

// SetDefaultEnvironment sets the default environment for operations
func (sm *StorageManager) SetDefaultEnvironment(env Environment) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.defaultEnv = env
}

// GetBackend returns the backend for a specific environment
func (sm *StorageManager) GetBackend(env Environment) (StorageBackend, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	backend, exists := sm.backends[env]
	if !exists {
		return nil, fmt.Errorf("no backend registered for environment: %s", env)
	}
	return backend, nil
}

// Get retrieves data from the default environment
func (sm *StorageManager) Get(key string) ([]byte, error) {
	return sm.GetFromEnv(sm.defaultEnv, key)
}

// Save stores data to the default environment
func (sm *StorageManager) Save(data []byte, key string) error {
	return sm.SaveToEnv(sm.defaultEnv, data, key)
}

// GetFromEnv retrieves data from a specific environment
func (sm *StorageManager) GetFromEnv(env Environment, key string) ([]byte, error) {
	backend, err := sm.GetBackend(env)
	if err != nil {
		return nil, err
	}
	return backend.Get(key)
}

// SaveToEnv stores data to a specific environment
func (sm *StorageManager) SaveToEnv(env Environment, data []byte, key string) error {
	backend, err := sm.GetBackend(env)
	if err != nil {
		return err
	}
	return backend.Save(data, key)
}

// CrossRead reads from one environment (typically production)
func (sm *StorageManager) CrossRead(key string) ([]byte, error) {
	return sm.GetFromEnv(EnvProduction, key)
}

// CrossWrite writes to another environment (typically staging/temp)
func (sm *StorageManager) CrossWrite(data []byte, key string) error {
	return sm.SaveToEnv(EnvStaging, data, key)
}

// CrossReadWrite performs a cross-environment operation: read from production, process, write to staging
func (sm *StorageManager) CrossReadWrite(key string, processor func([]byte) ([]byte, error)) error {
	// Read from production
	data, err := sm.GetFromEnv(EnvProduction, key)
	if err != nil {
		return fmt.Errorf("failed to read from production: %w", err)
	}
	
	// Process the data if processor is provided
	if processor != nil {
		data, err = processor(data)
		if err != nil {
			return fmt.Errorf("failed to process data: %w", err)
		}
	}
	
	// Write to staging
	if err := sm.SaveToEnv(EnvStaging, data, key); err != nil {
		return fmt.Errorf("failed to write to staging: %w", err)
	}
	
	return nil
}

// MergeEnvironments copies data from source environment to target environment
func (sm *StorageManager) MergeEnvironments(sourceEnv, targetEnv Environment, keys []string) error {
	for _, key := range keys {
		data, err := sm.GetFromEnv(sourceEnv, key)
		if err != nil {
			continue // Skip keys that don't exist in source
		}
		
		if data != nil {
			if err := sm.SaveToEnv(targetEnv, data, key); err != nil {
				return fmt.Errorf("failed to merge key %s from %s to %s: %w", key, sourceEnv, targetEnv, err)
			}
		}
	}
	return nil
}

// HasUpdates checks if source environment has updates compared to target environment
func (sm *StorageManager) HasUpdates(sourceEnv, targetEnv Environment, keys []string) (bool, error) {
	for _, key := range keys {
		sourceData, sourceErr := sm.GetFromEnv(sourceEnv, key)
		targetData, targetErr := sm.GetFromEnv(targetEnv, key)
		
		// If source has data but target doesn't, there's an update
		if sourceErr == nil && sourceData != nil && (targetErr != nil || targetData == nil) {
			return true, nil
		}
		
		// If both have data but they're different, there's an update
		if sourceErr == nil && sourceData != nil && targetErr == nil && targetData != nil {
			if len(sourceData) != len(targetData) {
				return true, nil
			}
		}
	}
	return false, nil
}

// Config returns the storage manager configuration
func (sm *StorageManager) Config() *StoreConfig {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	configs := make(map[string]interface{})
	for env, backend := range sm.backends {
		configs[string(env)] = backend.Config()
	}
	
	return &StoreConfig{
		Type:     "storage_manager",
		Path:     fmt.Sprintf("environments:%d", len(sm.backends)),
		ReadOnly: false,
		Options:  configs,
	}
}
