package store

import (
	"bufio"
	"bytes"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"hexcloud.cn/hicloud/menukit/pkg/utils"
)

// FileBackend implements StorageBackend for file-based storage
type FileBackend struct {
	baseDir string
	cache   map[string][]byte
	mutex   sync.RWMutex
	cacheTTL time.Duration
}

// NewFileBackend creates a new file-based storage backend
func NewFileBackend(baseDir string) *FileBackend {
	return &FileBackend{
		baseDir:  baseDir,
		cache:    make(map[string][]byte),
		cacheTTL: 5 * time.Minute,
	}
}

// Get retrieves data by key from the file system
func (fb *FileBackend) Get(key string) ([]byte, error) {
	filePath := utils.GetFilePath(fb.baseDir, key)
	
	// Check cache first
	fb.mutex.RLock()
	if cached, exists := fb.cache[filePath]; exists {
		fb.mutex.RUnlock()
		return cached, nil
	}
	fb.mutex.RUnlock()
	
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, nil
	}
	
	// Read from file system
	data, err := fb.bufferedReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}
	
	// Update cache
	fb.updateCache(filePath, data)
	
	return data, nil
}

// Save stores data with key to the file system
func (fb *FileBackend) Save(data []byte, key string) error {
	filePath := utils.GetFilePath(fb.baseDir, key)
	
	// Ensure directory exists
	dir := filepath.Dir(filePath)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
	}
	
	// Write to file system
	if err := fb.bufferedWriteFile(filePath, data); err != nil {
		return fmt.Errorf("failed to write file %s: %w", filePath, err)
	}
	
	// Update cache
	fb.updateCache(filePath, data)
	
	return nil
}

// Delete removes data by key from the file system
func (fb *FileBackend) Delete(key string) error {
	filePath := utils.GetFilePath(fb.baseDir, key)
	
	// Remove from cache
	fb.mutex.Lock()
	delete(fb.cache, filePath)
	fb.mutex.Unlock()
	
	// Remove from file system
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete file %s: %w", filePath, err)
	}
	
	return nil
}

// Exists checks if a key exists in the file system
func (fb *FileBackend) Exists(key string) (bool, error) {
	filePath := utils.GetFilePath(fb.baseDir, key)
	_, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return false, nil
	}
	if err != nil {
		return false, fmt.Errorf("failed to check file existence %s: %w", filePath, err)
	}
	return true, nil
}

// Config returns the backend configuration
func (fb *FileBackend) Config() *BackendConfig {
	return &BackendConfig{
		Type: "file",
		Path: fb.baseDir,
		Options: map[string]interface{}{
			"cache_ttl": fb.cacheTTL,
			"cache_size": len(fb.cache),
		},
	}
}

// bufferedReadFile reads a file with buffering
func (fb *FileBackend) bufferedReadFile(filePath string) ([]byte, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Use buffered reader
	reader := bufio.NewReaderSize(file, 64*1024) // 64KB buffer
	var buffer bytes.Buffer

	// Read file content
	_, err = io.CopyBuffer(&buffer, reader, make([]byte, 64*1024))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

// bufferedWriteFile writes a file with buffering
func (fb *FileBackend) bufferedWriteFile(filePath string, data []byte) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Use buffered writer
	writer := bufio.NewWriterSize(file, 64*1024) // 64KB buffer
	_, err = writer.Write(data)
	if err != nil {
		return err
	}

	// Ensure data is written to disk
	return writer.Flush()
}

// updateCache updates the internal cache
func (fb *FileBackend) updateCache(filePath string, data []byte) {
	fb.mutex.Lock()
	defer fb.mutex.Unlock()
	
	fb.cache[filePath] = data
	
	// Set up cache expiration
	time.AfterFunc(fb.cacheTTL, func() {
		fb.mutex.Lock()
		delete(fb.cache, filePath)
		fb.mutex.Unlock()
	})
}

// ClearCache clears the internal cache
func (fb *FileBackend) ClearCache() {
	fb.mutex.Lock()
	defer fb.mutex.Unlock()
	fb.cache = make(map[string][]byte)
}

// ClearCacheForKey clears cache for a specific key
func (fb *FileBackend) ClearCacheForKey(key string) {
	filePath := utils.GetFilePath(fb.baseDir, key)
	fb.mutex.Lock()
	defer fb.mutex.Unlock()
	delete(fb.cache, filePath)
}
