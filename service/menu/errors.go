package menu

import "errors"

// Storage-related errors
var (
	ErrNilStorageProvider = errors.New("storage provider cannot be nil")
	ErrNilSaveFunc        = errors.New("save function cannot be nil")
	ErrNilGetFunc         = errors.New("get function cannot be nil")
	ErrStorageOperation   = errors.New("storage operation failed")
)

// Configuration-related errors
var (
	ErrInvalidHost        = errors.New("host cannot be empty")
	ErrInvalidToken       = errors.New("token cannot be empty")
	ErrInvalidStoreID     = errors.New("store ID cannot be empty")
	ErrInvalidChannelCode = errors.New("channel code cannot be empty")
)
